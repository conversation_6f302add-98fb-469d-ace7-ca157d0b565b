"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@auth0";
exports.ids = ["vendor-chunks/@auth0"];
exports.modules = {

/***/ "(ssr)/./node_modules/@auth0/auth0-react/dist/auth0-react.esm.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@auth0/auth0-react/dist/auth0-react.esm.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Auth0Context: () => (/* binding */ Auth0Context),\n/* harmony export */   Auth0Provider: () => (/* binding */ Auth0Provider),\n/* harmony export */   AuthenticationError: () => (/* binding */ u),\n/* harmony export */   GenericError: () => (/* binding */ d),\n/* harmony export */   InMemoryCache: () => (/* binding */ P),\n/* harmony export */   LocalStorageCache: () => (/* binding */ z),\n/* harmony export */   MfaRequiredError: () => (/* binding */ m),\n/* harmony export */   MissingRefreshTokenError: () => (/* binding */ f),\n/* harmony export */   OAuthError: () => (/* binding */ OAuthError),\n/* harmony export */   PopupCancelledError: () => (/* binding */ p),\n/* harmony export */   PopupTimeoutError: () => (/* binding */ h),\n/* harmony export */   TimeoutError: () => (/* binding */ l),\n/* harmony export */   User: () => (/* binding */ ie),\n/* harmony export */   initialContext: () => (/* binding */ initialContext),\n/* harmony export */   useAuth0: () => (/* binding */ useAuth0),\n/* harmony export */   withAuth0: () => (/* binding */ withAuth0),\n/* harmony export */   withAuthenticationRequired: () => (/* binding */ withAuthenticationRequired)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nfunction __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nfunction e(e,t){var i={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(i[o]=e[o]);if(null!=e&&\"function\"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(i[o[n]]=e[o[n]]);}return i}\"function\"==typeof SuppressedError&&SuppressedError;var t=\"undefined\"!=typeof globalThis?globalThis:\"undefined\"!=typeof window?window:\"undefined\"!=typeof global?global:\"undefined\"!=typeof self?self:{};function i(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,\"default\")?e.default:e}function o(e,t){return e(t={exports:{}},t.exports),t.exports}var n=o((function(e,t){Object.defineProperty(t,\"__esModule\",{value:!0});var i=function(){function e(){var e=this;this.locked=new Map,this.addToLocked=function(t,i){var o=e.locked.get(t);void 0===o?void 0===i?e.locked.set(t,[]):e.locked.set(t,[i]):void 0!==i&&(o.unshift(i),e.locked.set(t,o));},this.isLocked=function(t){return e.locked.has(t)},this.lock=function(t){return new Promise((function(i,o){e.isLocked(t)?e.addToLocked(t,i):(e.addToLocked(t),i());}))},this.unlock=function(t){var i=e.locked.get(t);if(void 0!==i&&0!==i.length){var o=i.pop();e.locked.set(t,i),void 0!==o&&setTimeout(o,0);}else e.locked.delete(t);};}return e.getInstance=function(){return void 0===e.instance&&(e.instance=new e),e.instance},e}();t.default=function(){return i.getInstance()};}));i(n);var a=i(o((function(e,i){var o=t&&t.__awaiter||function(e,t,i,o){return new(i||(i=Promise))((function(n,a){function r(e){try{c(o.next(e));}catch(e){a(e);}}function s(e){try{c(o.throw(e));}catch(e){a(e);}}function c(e){e.done?n(e.value):new i((function(t){t(e.value);})).then(r,s);}c((o=o.apply(e,t||[])).next());}))},a=t&&t.__generator||function(e,t){var i,o,n,a,r={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},\"function\"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(i)throw new TypeError(\"Generator is already executing.\");for(;r;)try{if(i=1,o&&(n=2&a[0]?o.return:a[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,a[1])).done)return n;switch(o=0,n&&(a=[2&a[0],n.value]),a[0]){case 0:case 1:n=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,o=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(!(n=r.trys,(n=n.length>0&&n[n.length-1])||6!==a[0]&&2!==a[0])){r=0;continue}if(3===a[0]&&(!n||a[1]>n[0]&&a[1]<n[3])){r.label=a[1];break}if(6===a[0]&&r.label<n[1]){r.label=n[1],n=a;break}if(n&&r.label<n[2]){r.label=n[2],r.ops.push(a);break}n[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r);}catch(e){a=[6,e],o=0;}finally{i=n=0;}if(5&a[0])throw a[1];return {value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},r=t;Object.defineProperty(i,\"__esModule\",{value:!0});var s=\"browser-tabs-lock-key\",c={key:function(e){return o(r,void 0,void 0,(function(){return a(this,(function(e){throw new Error(\"Unsupported\")}))}))},getItem:function(e){return o(r,void 0,void 0,(function(){return a(this,(function(e){throw new Error(\"Unsupported\")}))}))},clear:function(){return o(r,void 0,void 0,(function(){return a(this,(function(e){return [2,window.localStorage.clear()]}))}))},removeItem:function(e){return o(r,void 0,void 0,(function(){return a(this,(function(e){throw new Error(\"Unsupported\")}))}))},setItem:function(e,t){return o(r,void 0,void 0,(function(){return a(this,(function(e){throw new Error(\"Unsupported\")}))}))},keySync:function(e){return window.localStorage.key(e)},getItemSync:function(e){return window.localStorage.getItem(e)},clearSync:function(){return window.localStorage.clear()},removeItemSync:function(e){return window.localStorage.removeItem(e)},setItemSync:function(e,t){return window.localStorage.setItem(e,t)}};function d(e){return new Promise((function(t){return setTimeout(t,e)}))}function u(e){for(var t=\"0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz\",i=\"\",o=0;o<e;o++){i+=t[Math.floor(Math.random()*t.length)];}return i}var l=function(){function e(t){this.acquiredIatSet=new Set,this.storageHandler=void 0,this.id=Date.now().toString()+u(15),this.acquireLock=this.acquireLock.bind(this),this.releaseLock=this.releaseLock.bind(this),this.releaseLock__private__=this.releaseLock__private__.bind(this),this.waitForSomethingToChange=this.waitForSomethingToChange.bind(this),this.refreshLockWhileAcquired=this.refreshLockWhileAcquired.bind(this),this.storageHandler=t,void 0===e.waiters&&(e.waiters=[]);}return e.prototype.acquireLock=function(t,i){return void 0===i&&(i=5e3),o(this,void 0,void 0,(function(){var o,n,r,l,h,p,m;return a(this,(function(a){switch(a.label){case 0:o=Date.now()+u(4),n=Date.now()+i,r=s+\"-\"+t,l=void 0===this.storageHandler?c:this.storageHandler,a.label=1;case 1:return Date.now()<n?[4,d(30)]:[3,8];case 2:return a.sent(),null!==l.getItemSync(r)?[3,5]:(h=this.id+\"-\"+t+\"-\"+o,[4,d(Math.floor(25*Math.random()))]);case 3:return a.sent(),l.setItemSync(r,JSON.stringify({id:this.id,iat:o,timeoutKey:h,timeAcquired:Date.now(),timeRefreshed:Date.now()})),[4,d(30)];case 4:return a.sent(),null!==(p=l.getItemSync(r))&&(m=JSON.parse(p)).id===this.id&&m.iat===o?(this.acquiredIatSet.add(o),this.refreshLockWhileAcquired(r,o),[2,!0]):[3,7];case 5:return e.lockCorrector(void 0===this.storageHandler?c:this.storageHandler),[4,this.waitForSomethingToChange(n)];case 6:a.sent(),a.label=7;case 7:return o=Date.now()+u(4),[3,1];case 8:return [2,!1]}}))}))},e.prototype.refreshLockWhileAcquired=function(e,t){return o(this,void 0,void 0,(function(){var i=this;return a(this,(function(r){return setTimeout((function(){return o(i,void 0,void 0,(function(){var i,o,r;return a(this,(function(a){switch(a.label){case 0:return [4,n.default().lock(t)];case 1:return a.sent(),this.acquiredIatSet.has(t)?(i=void 0===this.storageHandler?c:this.storageHandler,null===(o=i.getItemSync(e))?(n.default().unlock(t),[2]):((r=JSON.parse(o)).timeRefreshed=Date.now(),i.setItemSync(e,JSON.stringify(r)),n.default().unlock(t),this.refreshLockWhileAcquired(e,t),[2])):(n.default().unlock(t),[2])}}))}))}),1e3),[2]}))}))},e.prototype.waitForSomethingToChange=function(t){return o(this,void 0,void 0,(function(){return a(this,(function(i){switch(i.label){case 0:return [4,new Promise((function(i){var o=!1,n=Date.now(),a=!1;function r(){if(a||(window.removeEventListener(\"storage\",r),e.removeFromWaiting(r),clearTimeout(s),a=!0),!o){o=!0;var t=50-(Date.now()-n);t>0?setTimeout(i,t):i(null);}}window.addEventListener(\"storage\",r),e.addToWaiting(r);var s=setTimeout(r,Math.max(0,t-Date.now()));}))];case 1:return i.sent(),[2]}}))}))},e.addToWaiting=function(t){this.removeFromWaiting(t),void 0!==e.waiters&&e.waiters.push(t);},e.removeFromWaiting=function(t){void 0!==e.waiters&&(e.waiters=e.waiters.filter((function(e){return e!==t})));},e.notifyWaiters=function(){void 0!==e.waiters&&e.waiters.slice().forEach((function(e){return e()}));},e.prototype.releaseLock=function(e){return o(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return [4,this.releaseLock__private__(e)];case 1:return [2,t.sent()]}}))}))},e.prototype.releaseLock__private__=function(t){return o(this,void 0,void 0,(function(){var i,o,r,d;return a(this,(function(a){switch(a.label){case 0:return i=void 0===this.storageHandler?c:this.storageHandler,o=s+\"-\"+t,null===(r=i.getItemSync(o))?[2]:(d=JSON.parse(r)).id!==this.id?[3,2]:[4,n.default().lock(d.iat)];case 1:a.sent(),this.acquiredIatSet.delete(d.iat),i.removeItemSync(o),n.default().unlock(d.iat),e.notifyWaiters(),a.label=2;case 2:return [2]}}))}))},e.lockCorrector=function(t){for(var i=Date.now()-5e3,o=t,n=[],a=0;;){var r=o.keySync(a);if(null===r)break;n.push(r),a++;}for(var c=!1,d=0;d<n.length;d++){var u=n[d];if(u.includes(s)){var l=o.getItemSync(u);if(null!==l){var h=JSON.parse(l);(void 0===h.timeRefreshed&&h.timeAcquired<i||void 0!==h.timeRefreshed&&h.timeRefreshed<i)&&(o.removeItemSync(u),c=!0);}}}c&&e.notifyWaiters();},e.waiters=void 0,e}();i.default=l;})));const r={timeoutInSeconds:60},s={name:\"auth0-spa-js\",version:\"2.1.3\"},c=()=>Date.now();class d extends Error{constructor(e,t){super(t),this.error=e,this.error_description=t,Object.setPrototypeOf(this,d.prototype);}static fromPayload({error:e,error_description:t}){return new d(e,t)}}class u extends d{constructor(e,t,i,o=null){super(e,t),this.state=i,this.appState=o,Object.setPrototypeOf(this,u.prototype);}}class l extends d{constructor(){super(\"timeout\",\"Timeout\"),Object.setPrototypeOf(this,l.prototype);}}class h extends l{constructor(e){super(),this.popup=e,Object.setPrototypeOf(this,h.prototype);}}class p extends d{constructor(e){super(\"cancelled\",\"Popup closed\"),this.popup=e,Object.setPrototypeOf(this,p.prototype);}}class m extends d{constructor(e,t,i){super(e,t),this.mfa_token=i,Object.setPrototypeOf(this,m.prototype);}}class f extends d{constructor(e,t){super(\"missing_refresh_token\",`Missing Refresh Token (audience: '${g(e,[\"default\"])}', scope: '${g(t)}')`),this.audience=e,this.scope=t,Object.setPrototypeOf(this,f.prototype);}}function g(e,t=[]){return e&&!t.includes(e)?e:\"\"}const w=()=>window.crypto,y=()=>{const e=\"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~.\";let t=\"\";return Array.from(w().getRandomValues(new Uint8Array(43))).forEach((i=>t+=e[i%e.length])),t},k=e=>btoa(e),v=t=>{var{clientId:i}=t,o=e(t,[\"clientId\"]);return new URLSearchParams((e=>Object.keys(e).filter((t=>void 0!==e[t])).reduce(((t,i)=>Object.assign(Object.assign({},t),{[i]:e[i]})),{}))(Object.assign({client_id:i},o))).toString()},b=e=>(e=>decodeURIComponent(atob(e).split(\"\").map((e=>\"%\"+(\"00\"+e.charCodeAt(0).toString(16)).slice(-2))).join(\"\")))(e.replace(/_/g,\"/\").replace(/-/g,\"+\")),_=async(e,t)=>{const i=await fetch(e,t);return {ok:i.ok,json:await i.json()}},I=async(e,t,i)=>{const o=new AbortController;let n;return t.signal=o.signal,Promise.race([_(e,t),new Promise(((e,t)=>{n=setTimeout((()=>{o.abort(),t(new Error(\"Timeout when executing 'fetch'\"));}),i);}))]).finally((()=>{clearTimeout(n);}))},S=async(e,t,i,o,n,a,r)=>{return s={auth:{audience:t,scope:i},timeout:n,fetchUrl:e,fetchOptions:o,useFormData:r},c=a,new Promise((function(e,t){const i=new MessageChannel;i.port1.onmessage=function(o){o.data.error?t(new Error(o.data.error)):e(o.data),i.port1.close();},c.postMessage(s,[i.port2]);}));var s,c;},O=async(e,t,i,o,n,a,r=1e4)=>n?S(e,t,i,o,r,n,a):I(e,o,r);async function T(t,i){var{baseUrl:o,timeout:n,audience:a,scope:r,auth0Client:c,useFormData:u}=t,l=e(t,[\"baseUrl\",\"timeout\",\"audience\",\"scope\",\"auth0Client\",\"useFormData\"]);const h=u?v(l):JSON.stringify(l);return await async function(t,i,o,n,a,r,s){let c,u=null;for(let e=0;e<3;e++)try{c=await O(t,o,n,a,r,s,i),u=null;break}catch(e){u=e;}if(u)throw u;const l=c.json,{error:h,error_description:p}=l,g=e(l,[\"error\",\"error_description\"]),{ok:w}=c;if(!w){const e=p||`HTTP error. Unable to fetch ${t}`;if(\"mfa_required\"===h)throw new m(h,e,g.mfa_token);if(\"missing_refresh_token\"===h)throw new f(o,n);throw new d(h||\"request_error\",e)}return g}(`${o}/oauth/token`,n,a||\"default\",r,{method:\"POST\",body:h,headers:{\"Content-Type\":u?\"application/x-www-form-urlencoded\":\"application/json\",\"Auth0-Client\":btoa(JSON.stringify(c||s))}},i,u)}const j=(...e)=>{return (t=e.filter(Boolean).join(\" \").trim().split(/\\s+/),Array.from(new Set(t))).join(\" \");var t;};class C{constructor(e,t=\"@@auth0spajs@@\",i){this.prefix=t,this.suffix=i,this.clientId=e.clientId,this.scope=e.scope,this.audience=e.audience;}toKey(){return [this.prefix,this.clientId,this.audience,this.scope,this.suffix].filter(Boolean).join(\"::\")}static fromKey(e){const[t,i,o,n]=e.split(\"::\");return new C({clientId:i,scope:n,audience:o},t)}static fromCacheEntry(e){const{scope:t,audience:i,client_id:o}=e;return new C({scope:t,audience:i,clientId:o})}}class z{set(e,t){localStorage.setItem(e,JSON.stringify(t));}get(e){const t=window.localStorage.getItem(e);if(t)try{return JSON.parse(t)}catch(e){return}}remove(e){localStorage.removeItem(e);}allKeys(){return Object.keys(window.localStorage).filter((e=>e.startsWith(\"@@auth0spajs@@\")))}}class P{constructor(){this.enclosedCache=function(){let e={};return {set(t,i){e[t]=i;},get(t){const i=e[t];if(i)return i},remove(t){delete e[t];},allKeys:()=>Object.keys(e)}}();}}class x{constructor(e,t,i){this.cache=e,this.keyManifest=t,this.nowProvider=i||c;}async setIdToken(e,t,i){var o;const n=this.getIdTokenCacheKey(e);await this.cache.set(n,{id_token:t,decodedToken:i}),await(null===(o=this.keyManifest)||void 0===o?void 0:o.add(n));}async getIdToken(e){const t=await this.cache.get(this.getIdTokenCacheKey(e.clientId));if(!t&&e.scope&&e.audience){const t=await this.get(e);if(!t)return;if(!t.id_token||!t.decodedToken)return;return {id_token:t.id_token,decodedToken:t.decodedToken}}if(t)return {id_token:t.id_token,decodedToken:t.decodedToken}}async get(e,t=0){var i;let o=await this.cache.get(e.toKey());if(!o){const t=await this.getCacheKeys();if(!t)return;const i=this.matchExistingCacheKey(e,t);i&&(o=await this.cache.get(i));}if(!o)return;const n=await this.nowProvider(),a=Math.floor(n/1e3);return o.expiresAt-t<a?o.body.refresh_token?(o.body={refresh_token:o.body.refresh_token},await this.cache.set(e.toKey(),o),o.body):(await this.cache.remove(e.toKey()),void await(null===(i=this.keyManifest)||void 0===i?void 0:i.remove(e.toKey()))):o.body}async set(e){var t;const i=new C({clientId:e.client_id,scope:e.scope,audience:e.audience}),o=await this.wrapCacheEntry(e);await this.cache.set(i.toKey(),o),await(null===(t=this.keyManifest)||void 0===t?void 0:t.add(i.toKey()));}async clear(e){var t;const i=await this.getCacheKeys();i&&(await i.filter((t=>!e||t.includes(e))).reduce((async(e,t)=>{await e,await this.cache.remove(t);}),Promise.resolve()),await(null===(t=this.keyManifest)||void 0===t?void 0:t.clear()));}async wrapCacheEntry(e){const t=await this.nowProvider();return {body:e,expiresAt:Math.floor(t/1e3)+e.expires_in}}async getCacheKeys(){var e;return this.keyManifest?null===(e=await this.keyManifest.get())||void 0===e?void 0:e.keys:this.cache.allKeys?this.cache.allKeys():void 0}getIdTokenCacheKey(e){return new C({clientId:e},\"@@auth0spajs@@\",\"@@user@@\").toKey()}matchExistingCacheKey(e,t){return t.filter((t=>{var i;const o=C.fromKey(t),n=new Set(o.scope&&o.scope.split(\" \")),a=(null===(i=e.scope)||void 0===i?void 0:i.split(\" \"))||[],r=o.scope&&a.reduce(((e,t)=>e&&n.has(t)),!0);return \"@@auth0spajs@@\"===o.prefix&&o.clientId===e.clientId&&o.audience===e.audience&&r}))[0]}}class Z{constructor(e,t,i){this.storage=e,this.clientId=t,this.cookieDomain=i,this.storageKey=`a0.spajs.txs.${this.clientId}`;}create(e){this.storage.save(this.storageKey,e,{daysUntilExpire:1,cookieDomain:this.cookieDomain});}get(){return this.storage.get(this.storageKey)}remove(){this.storage.remove(this.storageKey,{cookieDomain:this.cookieDomain});}}const K=e=>\"number\"==typeof e,W=[\"iss\",\"aud\",\"exp\",\"nbf\",\"iat\",\"jti\",\"azp\",\"nonce\",\"auth_time\",\"at_hash\",\"c_hash\",\"acr\",\"amr\",\"sub_jwk\",\"cnf\",\"sip_from_tag\",\"sip_date\",\"sip_callid\",\"sip_cseq_num\",\"sip_via_branch\",\"orig\",\"dest\",\"mky\",\"events\",\"toe\",\"txn\",\"rph\",\"sid\",\"vot\",\"vtm\"],E=e=>{if(!e.id_token)throw new Error(\"ID token is required but missing\");const t=(e=>{const t=e.split(\".\"),[i,o,n]=t;if(3!==t.length||!i||!o||!n)throw new Error(\"ID token could not be decoded\");const a=JSON.parse(b(o)),r={__raw:e},s={};return Object.keys(a).forEach((e=>{r[e]=a[e],W.includes(e)||(s[e]=a[e]);})),{encoded:{header:i,payload:o,signature:n},header:JSON.parse(b(i)),claims:r,user:s}})(e.id_token);if(!t.claims.iss)throw new Error(\"Issuer (iss) claim must be a string present in the ID token\");if(t.claims.iss!==e.iss)throw new Error(`Issuer (iss) claim mismatch in the ID token; expected \"${e.iss}\", found \"${t.claims.iss}\"`);if(!t.user.sub)throw new Error(\"Subject (sub) claim must be a string present in the ID token\");if(\"RS256\"!==t.header.alg)throw new Error(`Signature algorithm of \"${t.header.alg}\" is not supported. Expected the ID token to be signed with \"RS256\".`);if(!t.claims.aud||\"string\"!=typeof t.claims.aud&&!Array.isArray(t.claims.aud))throw new Error(\"Audience (aud) claim must be a string or array of strings present in the ID token\");if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e.aud))throw new Error(`Audience (aud) claim mismatch in the ID token; expected \"${e.aud}\" but was not one of \"${t.claims.aud.join(\", \")}\"`);if(t.claims.aud.length>1){if(!t.claims.azp)throw new Error(\"Authorized Party (azp) claim must be a string present in the ID token when Audience (aud) claim has multiple values\");if(t.claims.azp!==e.aud)throw new Error(`Authorized Party (azp) claim mismatch in the ID token; expected \"${e.aud}\", found \"${t.claims.azp}\"`)}}else if(t.claims.aud!==e.aud)throw new Error(`Audience (aud) claim mismatch in the ID token; expected \"${e.aud}\" but found \"${t.claims.aud}\"`);if(e.nonce){if(!t.claims.nonce)throw new Error(\"Nonce (nonce) claim must be a string present in the ID token\");if(t.claims.nonce!==e.nonce)throw new Error(`Nonce (nonce) claim mismatch in the ID token; expected \"${e.nonce}\", found \"${t.claims.nonce}\"`)}if(e.max_age&&!K(t.claims.auth_time))throw new Error(\"Authentication Time (auth_time) claim must be a number present in the ID token when Max Age (max_age) is specified\");if(null==t.claims.exp||!K(t.claims.exp))throw new Error(\"Expiration Time (exp) claim must be a number present in the ID token\");if(!K(t.claims.iat))throw new Error(\"Issued At (iat) claim must be a number present in the ID token\");const i=e.leeway||60,o=new Date(e.now||Date.now()),n=new Date(0);if(n.setUTCSeconds(t.claims.exp+i),o>n)throw new Error(`Expiration Time (exp) claim error in the ID token; current time (${o}) is after expiration time (${n})`);if(null!=t.claims.nbf&&K(t.claims.nbf)){const e=new Date(0);if(e.setUTCSeconds(t.claims.nbf-i),o<e)throw new Error(`Not Before time (nbf) claim in the ID token indicates that this token can't be used just yet. Current time (${o}) is before ${e}`)}if(null!=t.claims.auth_time&&K(t.claims.auth_time)){const n=new Date(0);if(n.setUTCSeconds(parseInt(t.claims.auth_time)+e.max_age+i),o>n)throw new Error(`Authentication Time (auth_time) claim in the ID token indicates that too much time has passed since the last end-user authentication. Current time (${o}) is after last auth at ${n}`)}if(e.organization){const i=e.organization.trim();if(i.startsWith(\"org_\")){const e=i;if(!t.claims.org_id)throw new Error(\"Organization ID (org_id) claim must be a string present in the ID token\");if(e!==t.claims.org_id)throw new Error(`Organization ID (org_id) claim mismatch in the ID token; expected \"${e}\", found \"${t.claims.org_id}\"`)}else {const e=i.toLowerCase();if(!t.claims.org_name)throw new Error(\"Organization Name (org_name) claim must be a string present in the ID token\");if(e!==t.claims.org_name)throw new Error(`Organization Name (org_name) claim mismatch in the ID token; expected \"${e}\", found \"${t.claims.org_name}\"`)}}return t};var R=o((function(e,i){var o=t&&t.__assign||function(){return o=Object.assign||function(e){for(var t,i=1,o=arguments.length;i<o;i++)for(var n in t=arguments[i])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},o.apply(this,arguments)};function n(e,t){if(!t)return \"\";var i=\"; \"+e;return !0===t?i:i+\"=\"+t}function a(e,t,i){return encodeURIComponent(e).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/\\(/g,\"%28\").replace(/\\)/g,\"%29\")+\"=\"+encodeURIComponent(t).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent)+function(e){if(\"number\"==typeof e.expires){var t=new Date;t.setMilliseconds(t.getMilliseconds()+864e5*e.expires),e.expires=t;}return n(\"Expires\",e.expires?e.expires.toUTCString():\"\")+n(\"Domain\",e.domain)+n(\"Path\",e.path)+n(\"Secure\",e.secure)+n(\"SameSite\",e.sameSite)}(i)}function r(e){for(var t={},i=e?e.split(\"; \"):[],o=/(%[\\dA-F]{2})+/gi,n=0;n<i.length;n++){var a=i[n].split(\"=\"),r=a.slice(1).join(\"=\");'\"'===r.charAt(0)&&(r=r.slice(1,-1));try{t[a[0].replace(o,decodeURIComponent)]=r.replace(o,decodeURIComponent);}catch(e){}}return t}function s(){return r(document.cookie)}function c(e,t,i){document.cookie=a(e,t,o({path:\"/\"},i));}i.__esModule=!0,i.encode=a,i.parse=r,i.getAll=s,i.get=function(e){return s()[e]},i.set=c,i.remove=function(e,t){c(e,\"\",o(o({},t),{expires:-1}));};}));i(R),R.encode,R.parse,R.getAll;var U=R.get,L=R.set,D=R.remove;const X={get(e){const t=U(e);if(void 0!==t)return JSON.parse(t)},save(e,t,i){let o={};\"https:\"===window.location.protocol&&(o={secure:!0,sameSite:\"none\"}),(null==i?void 0:i.daysUntilExpire)&&(o.expires=i.daysUntilExpire),(null==i?void 0:i.cookieDomain)&&(o.domain=i.cookieDomain),L(e,JSON.stringify(t),o);},remove(e,t){let i={};(null==t?void 0:t.cookieDomain)&&(i.domain=t.cookieDomain),D(e,i);}},N={get(e){const t=X.get(e);return t||X.get(`_legacy_${e}`)},save(e,t,i){let o={};\"https:\"===window.location.protocol&&(o={secure:!0}),(null==i?void 0:i.daysUntilExpire)&&(o.expires=i.daysUntilExpire),(null==i?void 0:i.cookieDomain)&&(o.domain=i.cookieDomain),L(`_legacy_${e}`,JSON.stringify(t),o),X.save(e,t,i);},remove(e,t){let i={};(null==t?void 0:t.cookieDomain)&&(i.domain=t.cookieDomain),D(e,i),X.remove(e,t),X.remove(`_legacy_${e}`,t);}},J={get(e){if(\"undefined\"==typeof sessionStorage)return;const t=sessionStorage.getItem(e);return null!=t?JSON.parse(t):void 0},save(e,t){sessionStorage.setItem(e,JSON.stringify(t));},remove(e){sessionStorage.removeItem(e);}};function F(e,t,i){var o=void 0===t?null:t,n=function(e,t){var i=atob(e);if(t){for(var o=new Uint8Array(i.length),n=0,a=i.length;n<a;++n)o[n]=i.charCodeAt(n);return String.fromCharCode.apply(null,new Uint16Array(o.buffer))}return i}(e,void 0!==i&&i),a=n.indexOf(\"\\n\",10)+1,r=n.substring(a)+(o?\"//# sourceMappingURL=\"+o:\"\"),s=new Blob([r],{type:\"application/javascript\"});return URL.createObjectURL(s)}var H,Y,G,V,M=(H=\"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\",Y=null,G=!1,function(e){return V=V||F(H,Y,G),new Worker(V,e)});const A={};class B{constructor(e,t){this.cache=e,this.clientId=t,this.manifestKey=this.createManifestKeyFrom(this.clientId);}async add(e){var t;const i=new Set((null===(t=await this.cache.get(this.manifestKey))||void 0===t?void 0:t.keys)||[]);i.add(e),await this.cache.set(this.manifestKey,{keys:[...i]});}async remove(e){const t=await this.cache.get(this.manifestKey);if(t){const i=new Set(t.keys);return i.delete(e),i.size>0?await this.cache.set(this.manifestKey,{keys:[...i]}):await this.cache.remove(this.manifestKey)}}get(){return this.cache.get(this.manifestKey)}clear(){return this.cache.remove(this.manifestKey)}createManifestKeyFrom(e){return `@@auth0spajs@@::${e}`}}const $={memory:()=>(new P).enclosedCache,localstorage:()=>new z},q=e=>$[e],Q=t=>{const{openUrl:i,onRedirect:o}=t,n=e(t,[\"openUrl\",\"onRedirect\"]);return Object.assign(Object.assign({},n),{openUrl:!1===i||i?i:o})},ee=new a;class te{constructor(e){let t,i;if(this.userCache=(new P).enclosedCache,this.defaultOptions={authorizationParams:{scope:\"openid profile email\"},useRefreshTokensFallback:!1,useFormData:!0},this._releaseLockOnPageHide=async()=>{await ee.releaseLock(\"auth0.lock.getTokenSilently\"),window.removeEventListener(\"pagehide\",this._releaseLockOnPageHide);},this.options=Object.assign(Object.assign(Object.assign({},this.defaultOptions),e),{authorizationParams:Object.assign(Object.assign({},this.defaultOptions.authorizationParams),e.authorizationParams)}),\"undefined\"!=typeof window&&(()=>{if(!w())throw new Error(\"For security reasons, `window.crypto` is required to run `auth0-spa-js`.\");if(void 0===w().subtle)throw new Error(\"\\n      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/main/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.\\n    \")})(),e.cache&&e.cacheLocation&&console.warn(\"Both `cache` and `cacheLocation` options have been specified in the Auth0Client configuration; ignoring `cacheLocation` and using `cache`.\"),e.cache)i=e.cache;else {if(t=e.cacheLocation||\"memory\",!q(t))throw new Error(`Invalid cache location \"${t}\"`);i=q(t)();}this.httpTimeoutMs=e.httpTimeoutInSeconds?1e3*e.httpTimeoutInSeconds:1e4,this.cookieStorage=!1===e.legacySameSiteCookie?X:N,this.orgHintCookieName=`auth0.${this.options.clientId}.organization_hint`,this.isAuthenticatedCookieName=(e=>`auth0.${e}.is.authenticated`)(this.options.clientId),this.sessionCheckExpiryDays=e.sessionCheckExpiryDays||1;const o=e.useCookiesForTransactions?this.cookieStorage:J;var n;this.scope=j(\"openid\",this.options.authorizationParams.scope,this.options.useRefreshTokens?\"offline_access\":\"\"),this.transactionManager=new Z(o,this.options.clientId,this.options.cookieDomain),this.nowProvider=this.options.nowProvider||c,this.cacheManager=new x(i,i.allKeys?void 0:new B(i,this.options.clientId),this.nowProvider),this.domainUrl=(n=this.options.domain,/^https?:\\/\\//.test(n)?n:`https://${n}`),this.tokenIssuer=((e,t)=>e?e.startsWith(\"https://\")?e:`https://${e}/`:`${t}/`)(this.options.issuer,this.domainUrl),\"undefined\"!=typeof window&&window.Worker&&this.options.useRefreshTokens&&\"memory\"===t&&(this.options.workerUrl?this.worker=new Worker(this.options.workerUrl):this.worker=new M);}_url(e){const t=encodeURIComponent(btoa(JSON.stringify(this.options.auth0Client||s)));return `${this.domainUrl}${e}&auth0Client=${t}`}_authorizeUrl(e){return this._url(`/authorize?${v(e)}`)}async _verifyIdToken(e,t,i){const o=await this.nowProvider();return E({iss:this.tokenIssuer,aud:this.options.clientId,id_token:e,nonce:t,organization:i,leeway:this.options.leeway,max_age:(n=this.options.authorizationParams.max_age,\"string\"!=typeof n?n:parseInt(n,10)||void 0),now:o});var n;}_processOrgHint(e){e?this.cookieStorage.save(this.orgHintCookieName,e,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}):this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain});}async _prepareAuthorizeUrl(e,t,i){const o=k(y()),n=k(y()),a=y(),r=(e=>{const t=new Uint8Array(e);return (e=>{const t={\"+\":\"-\",\"/\":\"_\",\"=\":\"\"};return e.replace(/[+/=]/g,(e=>t[e]))})(window.btoa(String.fromCharCode(...Array.from(t))))})(await(async e=>{const t=w().subtle.digest({name:\"SHA-256\"},(new TextEncoder).encode(e));return await t})(a)),s=((e,t,i,o,n,a,r,s)=>Object.assign(Object.assign(Object.assign({client_id:e.clientId},e.authorizationParams),i),{scope:j(t,i.scope),response_type:\"code\",response_mode:s||\"query\",state:o,nonce:n,redirect_uri:r||e.authorizationParams.redirect_uri,code_challenge:a,code_challenge_method:\"S256\"}))(this.options,this.scope,e,o,n,r,e.redirect_uri||this.options.authorizationParams.redirect_uri||i,null==t?void 0:t.response_mode),c=this._authorizeUrl(s);return {nonce:n,code_verifier:a,scope:s.scope,audience:s.audience||\"default\",redirect_uri:s.redirect_uri,state:o,url:c}}async loginWithPopup(e,t){var i;if(e=e||{},!(t=t||{}).popup&&(t.popup=(e=>{const t=window.screenX+(window.innerWidth-400)/2,i=window.screenY+(window.innerHeight-600)/2;return window.open(e,\"auth0:authorize:popup\",`left=${t},top=${i},width=400,height=600,resizable,scrollbars=yes,status=1`)})(\"\"),!t.popup))throw new Error(\"Unable to open a popup for loginWithPopup - window.open returned `null`\");const o=await this._prepareAuthorizeUrl(e.authorizationParams||{},{response_mode:\"web_message\"},window.location.origin);t.popup.location.href=o.url;const n=await(e=>new Promise(((t,i)=>{let o;const n=setInterval((()=>{e.popup&&e.popup.closed&&(clearInterval(n),clearTimeout(a),window.removeEventListener(\"message\",o,!1),i(new p(e.popup)));}),1e3),a=setTimeout((()=>{clearInterval(n),i(new h(e.popup)),window.removeEventListener(\"message\",o,!1);}),1e3*(e.timeoutInSeconds||60));o=function(r){if(r.data&&\"authorization_response\"===r.data.type){if(clearTimeout(a),clearInterval(n),window.removeEventListener(\"message\",o,!1),e.popup.close(),r.data.response.error)return i(d.fromPayload(r.data.response));t(r.data.response);}},window.addEventListener(\"message\",o);})))(Object.assign(Object.assign({},t),{timeoutInSeconds:t.timeoutInSeconds||this.options.authorizeTimeoutInSeconds||60}));if(o.state!==n.state)throw new d(\"state_mismatch\",\"Invalid state\");const a=(null===(i=e.authorizationParams)||void 0===i?void 0:i.organization)||this.options.authorizationParams.organization;await this._requestToken({audience:o.audience,scope:o.scope,code_verifier:o.code_verifier,grant_type:\"authorization_code\",code:n.code,redirect_uri:o.redirect_uri},{nonceIn:o.nonce,organization:a});}async getUser(){var e;const t=await this._getIdTokenFromCache();return null===(e=null==t?void 0:t.decodedToken)||void 0===e?void 0:e.user}async getIdTokenClaims(){var e;const t=await this._getIdTokenFromCache();return null===(e=null==t?void 0:t.decodedToken)||void 0===e?void 0:e.claims}async loginWithRedirect(t={}){var i;const o=Q(t),{openUrl:n,fragment:a,appState:r}=o,s=e(o,[\"openUrl\",\"fragment\",\"appState\"]),c=(null===(i=s.authorizationParams)||void 0===i?void 0:i.organization)||this.options.authorizationParams.organization,d=await this._prepareAuthorizeUrl(s.authorizationParams||{}),{url:u}=d,l=e(d,[\"url\"]);this.transactionManager.create(Object.assign(Object.assign(Object.assign({},l),{appState:r}),c&&{organization:c}));const h=a?`${u}#${a}`:u;n?await n(h):window.location.assign(h);}async handleRedirectCallback(e=window.location.href){const t=e.split(\"?\").slice(1);if(0===t.length)throw new Error(\"There are no query params available for parsing.\");const{state:i,code:o,error:n,error_description:a}=(e=>{e.indexOf(\"#\")>-1&&(e=e.substring(0,e.indexOf(\"#\")));const t=new URLSearchParams(e);return {state:t.get(\"state\"),code:t.get(\"code\")||void 0,error:t.get(\"error\")||void 0,error_description:t.get(\"error_description\")||void 0}})(t.join(\"\")),r=this.transactionManager.get();if(!r)throw new d(\"missing_transaction\",\"Invalid state\");if(this.transactionManager.remove(),n)throw new u(n,a||n,i,r.appState);if(!r.code_verifier||r.state&&r.state!==i)throw new d(\"state_mismatch\",\"Invalid state\");const s=r.organization,c=r.nonce,l=r.redirect_uri;return await this._requestToken(Object.assign({audience:r.audience,scope:r.scope,code_verifier:r.code_verifier,grant_type:\"authorization_code\",code:o},l?{redirect_uri:l}:{}),{nonceIn:c,organization:s}),{appState:r.appState}}async checkSession(e){if(!this.cookieStorage.get(this.isAuthenticatedCookieName)){if(!this.cookieStorage.get(\"auth0.is.authenticated\"))return;this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove(\"auth0.is.authenticated\");}try{await this.getTokenSilently(e);}catch(e){}}async getTokenSilently(e={}){var t;const i=Object.assign(Object.assign({cacheMode:\"on\"},e),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),e.authorizationParams),{scope:j(this.scope,null===(t=e.authorizationParams)||void 0===t?void 0:t.scope)})}),o=await((e,t)=>{let i=A[t];return i||(i=e().finally((()=>{delete A[t],i=null;})),A[t]=i),i})((()=>this._getTokenSilently(i)),`${this.options.clientId}::${i.authorizationParams.audience}::${i.authorizationParams.scope}`);return e.detailedResponse?o:null==o?void 0:o.access_token}async _getTokenSilently(t){const{cacheMode:i}=t,o=e(t,[\"cacheMode\"]);if(\"off\"!==i){const e=await this._getEntryFromCache({scope:o.authorizationParams.scope,audience:o.authorizationParams.audience||\"default\",clientId:this.options.clientId});if(e)return e}if(\"cache-only\"!==i){if(!await(async(e,t=3)=>{for(let i=0;i<t;i++)if(await e())return !0;return !1})((()=>ee.acquireLock(\"auth0.lock.getTokenSilently\",5e3)),10))throw new l;try{if(window.addEventListener(\"pagehide\",this._releaseLockOnPageHide),\"off\"!==i){const e=await this._getEntryFromCache({scope:o.authorizationParams.scope,audience:o.authorizationParams.audience||\"default\",clientId:this.options.clientId});if(e)return e}const e=this.options.useRefreshTokens?await this._getTokenUsingRefreshToken(o):await this._getTokenFromIFrame(o),{id_token:t,access_token:n,oauthTokenScope:a,expires_in:r}=e;return Object.assign(Object.assign({id_token:t,access_token:n},a?{scope:a}:null),{expires_in:r})}finally{await ee.releaseLock(\"auth0.lock.getTokenSilently\"),window.removeEventListener(\"pagehide\",this._releaseLockOnPageHide);}}}async getTokenWithPopup(e={},t={}){var i;const o=Object.assign(Object.assign({},e),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),e.authorizationParams),{scope:j(this.scope,null===(i=e.authorizationParams)||void 0===i?void 0:i.scope)})});t=Object.assign(Object.assign({},r),t),await this.loginWithPopup(o,t);return (await this.cacheManager.get(new C({scope:o.authorizationParams.scope,audience:o.authorizationParams.audience||\"default\",clientId:this.options.clientId}))).access_token}async isAuthenticated(){return !!await this.getUser()}_buildLogoutUrl(t){null!==t.clientId?t.clientId=t.clientId||this.options.clientId:delete t.clientId;const i=t.logoutParams||{},{federated:o}=i,n=e(i,[\"federated\"]),a=o?\"&federated\":\"\";return this._url(`/v2/logout?${v(Object.assign({clientId:t.clientId},n))}`)+a}async logout(t={}){const i=Q(t),{openUrl:o}=i,n=e(i,[\"openUrl\"]);null===t.clientId?await this.cacheManager.clear():await this.cacheManager.clear(t.clientId||this.options.clientId),this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove(this.isAuthenticatedCookieName,{cookieDomain:this.options.cookieDomain}),this.userCache.remove(\"@@user@@\");const a=this._buildLogoutUrl(n);o?await o(a):!1!==o&&window.location.assign(a);}async _getTokenFromIFrame(e){const t=Object.assign(Object.assign({},e.authorizationParams),{prompt:\"none\"}),i=this.cookieStorage.get(this.orgHintCookieName);i&&!t.organization&&(t.organization=i);const{url:o,state:n,nonce:a,code_verifier:r,redirect_uri:s,scope:c,audience:u}=await this._prepareAuthorizeUrl(t,{response_mode:\"web_message\"},window.location.origin);try{if(window.crossOriginIsolated)throw new d(\"login_required\",\"The application is running in a Cross-Origin Isolated context, silently retrieving a token without refresh token is not possible.\");const i=e.timeoutInSeconds||this.options.authorizeTimeoutInSeconds,h=await((e,t,i=60)=>new Promise(((o,n)=>{const a=window.document.createElement(\"iframe\");a.setAttribute(\"width\",\"0\"),a.setAttribute(\"height\",\"0\"),a.style.display=\"none\";const r=()=>{window.document.body.contains(a)&&(window.document.body.removeChild(a),window.removeEventListener(\"message\",s,!1));};let s;const c=setTimeout((()=>{n(new l),r();}),1e3*i);s=function(e){if(e.origin!=t)return;if(!e.data||\"authorization_response\"!==e.data.type)return;const i=e.source;i&&i.close(),e.data.response.error?n(d.fromPayload(e.data.response)):o(e.data.response),clearTimeout(c),window.removeEventListener(\"message\",s,!1),setTimeout(r,2e3);},window.addEventListener(\"message\",s,!1),window.document.body.appendChild(a),a.setAttribute(\"src\",e);})))(o,this.domainUrl,i);if(n!==h.state)throw new d(\"state_mismatch\",\"Invalid state\");const p=await this._requestToken(Object.assign(Object.assign({},e.authorizationParams),{code_verifier:r,code:h.code,grant_type:\"authorization_code\",redirect_uri:s,timeout:e.authorizationParams.timeout||this.httpTimeoutMs}),{nonceIn:a,organization:t.organization});return Object.assign(Object.assign({},p),{scope:c,oauthTokenScope:p.scope,audience:u})}catch(e){throw \"login_required\"===e.error&&this.logout({openUrl:!1}),e}}async _getTokenUsingRefreshToken(e){const t=await this.cacheManager.get(new C({scope:e.authorizationParams.scope,audience:e.authorizationParams.audience||\"default\",clientId:this.options.clientId}));if(!(t&&t.refresh_token||this.worker)){if(this.options.useRefreshTokensFallback)return await this._getTokenFromIFrame(e);throw new f(e.authorizationParams.audience||\"default\",e.authorizationParams.scope)}const i=e.authorizationParams.redirect_uri||this.options.authorizationParams.redirect_uri||window.location.origin,o=\"number\"==typeof e.timeoutInSeconds?1e3*e.timeoutInSeconds:null;try{const n=await this._requestToken(Object.assign(Object.assign(Object.assign({},e.authorizationParams),{grant_type:\"refresh_token\",refresh_token:t&&t.refresh_token,redirect_uri:i}),o&&{timeout:o}));return Object.assign(Object.assign({},n),{scope:e.authorizationParams.scope,oauthTokenScope:n.scope,audience:e.authorizationParams.audience||\"default\"})}catch(t){if((t.message.indexOf(\"Missing Refresh Token\")>-1||t.message&&t.message.indexOf(\"invalid refresh token\")>-1)&&this.options.useRefreshTokensFallback)return await this._getTokenFromIFrame(e);throw t}}async _saveEntryInCache(t){const{id_token:i,decodedToken:o}=t,n=e(t,[\"id_token\",\"decodedToken\"]);this.userCache.set(\"@@user@@\",{id_token:i,decodedToken:o}),await this.cacheManager.setIdToken(this.options.clientId,t.id_token,t.decodedToken),await this.cacheManager.set(n);}async _getIdTokenFromCache(){const e=this.options.authorizationParams.audience||\"default\",t=await this.cacheManager.getIdToken(new C({clientId:this.options.clientId,audience:e,scope:this.scope})),i=this.userCache.get(\"@@user@@\");return t&&t.id_token===(null==i?void 0:i.id_token)?i:(this.userCache.set(\"@@user@@\",t),t)}async _getEntryFromCache({scope:e,audience:t,clientId:i}){const o=await this.cacheManager.get(new C({scope:e,audience:t,clientId:i}),60);if(o&&o.access_token){const{access_token:e,oauthTokenScope:t,expires_in:i}=o,n=await this._getIdTokenFromCache();return n&&Object.assign(Object.assign({id_token:n.id_token,access_token:e},t?{scope:t}:null),{expires_in:i})}}async _requestToken(e,t){const{nonceIn:i,organization:o}=t||{},n=await T(Object.assign({baseUrl:this.domainUrl,client_id:this.options.clientId,auth0Client:this.options.auth0Client,useFormData:this.options.useFormData,timeout:this.httpTimeoutMs},e),this.worker),a=await this._verifyIdToken(n.id_token,i,o);return await this._saveEntryInCache(Object.assign(Object.assign(Object.assign(Object.assign({},n),{decodedToken:a,scope:e.scope,audience:e.audience||\"default\"}),n.scope?{oauthTokenScope:n.scope}:null),{client_id:this.options.clientId})),this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this._processOrgHint(o||a.claims.org_id),Object.assign(Object.assign({},n),{decodedToken:a})}}class ie{}\n\n/**\r\n * The initial auth state.\r\n */\r\nvar initialAuthState = {\r\n    isAuthenticated: false,\r\n    isLoading: true,\r\n};\n\n/**\r\n * @ignore\r\n */\r\nvar stub = function () {\r\n    throw new Error('You forgot to wrap your component in <Auth0Provider>.');\r\n};\r\n/**\r\n * @ignore\r\n */\r\nvar initialContext = __assign(__assign({}, initialAuthState), { buildAuthorizeUrl: stub, buildLogoutUrl: stub, getAccessTokenSilently: stub, getAccessTokenWithPopup: stub, getIdTokenClaims: stub, loginWithRedirect: stub, loginWithPopup: stub, logout: stub, handleRedirectCallback: stub });\r\n/**\r\n * The Auth0 Context\r\n */\r\nvar Auth0Context = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(initialContext);\n\n/**\r\n * An OAuth2 error will come from the authorization server and will have at least an `error` property which will\r\n * be the error code. And possibly an `error_description` property\r\n *\r\n * See: https://openid.net/specs/openid-connect-core-1_0.html#rfc.section.*******\r\n */\r\nvar OAuthError = /** @class */ (function (_super) {\r\n    __extends(OAuthError, _super);\r\n    function OAuthError(error, error_description) {\r\n        var _this = _super.call(this, error_description || error) || this;\r\n        _this.error = error;\r\n        _this.error_description = error_description;\r\n        // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\r\n        Object.setPrototypeOf(_this, OAuthError.prototype);\r\n        return _this;\r\n    }\r\n    return OAuthError;\r\n}(Error));\n\nvar CODE_RE = /[?&]code=[^&]+/;\r\nvar STATE_RE = /[?&]state=[^&]+/;\r\nvar ERROR_RE = /[?&]error=[^&]+/;\r\nvar hasAuthParams = function (searchParams) {\r\n    if (searchParams === void 0) { searchParams = window.location.search; }\r\n    return (CODE_RE.test(searchParams) || ERROR_RE.test(searchParams)) &&\r\n        STATE_RE.test(searchParams);\r\n};\r\nvar normalizeErrorFn = function (fallbackMessage) {\r\n    return function (error) {\r\n        if (error instanceof Error) {\r\n            return error;\r\n        }\r\n        // try to check errors of the following form: {error: string; error_description?: string}\r\n        if (error !== null &&\r\n            typeof error === 'object' &&\r\n            'error' in error &&\r\n            typeof error.error === 'string') {\r\n            if ('error_description' in error &&\r\n                typeof error.error_description === 'string') {\r\n                return new OAuthError(error.error, error.error_description);\r\n            }\r\n            return new OAuthError(error.error);\r\n        }\r\n        return new Error(fallbackMessage);\r\n    };\r\n};\r\nvar loginError = normalizeErrorFn('Login failed');\r\nvar tokenError = normalizeErrorFn('Get access token failed');\r\n/**\r\n * @ignore\r\n * Helper function to map the v1 `redirectUri` option to the v2 `authorizationParams.redirect_uri`\r\n * and log a warning.\r\n */\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nvar deprecateRedirectUri = function (options) {\r\n    var _a;\r\n    if (options === null || options === void 0 ? void 0 : options.redirectUri) {\r\n        console.warn('Using `redirectUri` has been deprecated, please use `authorizationParams.redirect_uri` instead as `redirectUri` will be no longer supported in a future version');\r\n        options.authorizationParams = options.authorizationParams || {};\r\n        options.authorizationParams.redirect_uri = options.redirectUri;\r\n        delete options.redirectUri;\r\n    }\r\n    if ((_a = options === null || options === void 0 ? void 0 : options.authorizationParams) === null || _a === void 0 ? void 0 : _a.redirectUri) {\r\n        console.warn('Using `authorizationParams.redirectUri` has been deprecated, please use `authorizationParams.redirect_uri` instead as `authorizationParams.redirectUri` will be removed in a future version');\r\n        options.authorizationParams.redirect_uri =\r\n            options.authorizationParams.redirectUri;\r\n        delete options.authorizationParams.redirectUri;\r\n    }\r\n};\n\n/**\r\n * Handles how that state changes in the `useAuth0` hook.\r\n */\r\nvar reducer = function (state, action) {\r\n    switch (action.type) {\r\n        case 'LOGIN_POPUP_STARTED':\r\n            return __assign(__assign({}, state), { isLoading: true });\r\n        case 'LOGIN_POPUP_COMPLETE':\r\n        case 'INITIALISED':\r\n            return __assign(__assign({}, state), { isAuthenticated: !!action.user, user: action.user, isLoading: false, error: undefined });\r\n        case 'HANDLE_REDIRECT_COMPLETE':\r\n        case 'GET_ACCESS_TOKEN_COMPLETE':\r\n            if (state.user === action.user) {\r\n                return state;\r\n            }\r\n            return __assign(__assign({}, state), { isAuthenticated: !!action.user, user: action.user });\r\n        case 'LOGOUT':\r\n            return __assign(__assign({}, state), { isAuthenticated: false, user: undefined });\r\n        case 'ERROR':\r\n            return __assign(__assign({}, state), { isLoading: false, error: action.error });\r\n    }\r\n};\n\n/**\r\n * @ignore\r\n */\r\nvar toAuth0ClientOptions = function (opts) {\r\n    deprecateRedirectUri(opts);\r\n    return __assign(__assign({}, opts), { auth0Client: {\r\n            name: 'auth0-react',\r\n            version: '2.2.4',\r\n        } });\r\n};\r\n/**\r\n * @ignore\r\n */\r\nvar defaultOnRedirectCallback = function (appState) {\r\n    window.history.replaceState({}, document.title, (appState === null || appState === void 0 ? void 0 : appState.returnTo) || window.location.pathname);\r\n};\r\n/**\r\n * ```jsx\r\n * <Auth0Provider\r\n *   domain={domain}\r\n *   clientId={clientId}\r\n *   authorizationParams={{ redirect_uri: window.location.origin }}}>\r\n *   <MyApp />\r\n * </Auth0Provider>\r\n * ```\r\n *\r\n * Provides the Auth0Context to its child components.\r\n */\r\nvar Auth0Provider = function (opts) {\r\n    var children = opts.children, skipRedirectCallback = opts.skipRedirectCallback, _a = opts.onRedirectCallback, onRedirectCallback = _a === void 0 ? defaultOnRedirectCallback : _a, _b = opts.context, context = _b === void 0 ? Auth0Context : _b, clientOpts = __rest(opts, [\"children\", \"skipRedirectCallback\", \"onRedirectCallback\", \"context\"]);\r\n    var client = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function () { return new te(toAuth0ClientOptions(clientOpts)); })[0];\r\n    var _c = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, initialAuthState), state = _c[0], dispatch = _c[1];\r\n    var didInitialise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\r\n        if (didInitialise.current) {\r\n            return;\r\n        }\r\n        didInitialise.current = true;\r\n        (function () { return __awaiter(void 0, void 0, void 0, function () {\r\n            var user, appState, error_1;\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0:\r\n                        _a.trys.push([0, 7, , 8]);\r\n                        user = void 0;\r\n                        if (!(hasAuthParams() && !skipRedirectCallback)) return [3 /*break*/, 3];\r\n                        return [4 /*yield*/, client.handleRedirectCallback()];\r\n                    case 1:\r\n                        appState = (_a.sent()).appState;\r\n                        return [4 /*yield*/, client.getUser()];\r\n                    case 2:\r\n                        user = _a.sent();\r\n                        onRedirectCallback(appState, user);\r\n                        return [3 /*break*/, 6];\r\n                    case 3: return [4 /*yield*/, client.checkSession()];\r\n                    case 4:\r\n                        _a.sent();\r\n                        return [4 /*yield*/, client.getUser()];\r\n                    case 5:\r\n                        user = _a.sent();\r\n                        _a.label = 6;\r\n                    case 6:\r\n                        dispatch({ type: 'INITIALISED', user: user });\r\n                        return [3 /*break*/, 8];\r\n                    case 7:\r\n                        error_1 = _a.sent();\r\n                        dispatch({ type: 'ERROR', error: loginError(error_1) });\r\n                        return [3 /*break*/, 8];\r\n                    case 8: return [2 /*return*/];\r\n                }\r\n            });\r\n        }); })();\r\n    }, [client, onRedirectCallback, skipRedirectCallback]);\r\n    var loginWithRedirect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (opts) {\r\n        deprecateRedirectUri(opts);\r\n        return client.loginWithRedirect(opts);\r\n    }, [client]);\r\n    var loginWithPopup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (options, config) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var error_2, user;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    dispatch({ type: 'LOGIN_POPUP_STARTED' });\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, 3, , 4]);\r\n                    return [4 /*yield*/, client.loginWithPopup(options, config)];\r\n                case 2:\r\n                    _a.sent();\r\n                    return [3 /*break*/, 4];\r\n                case 3:\r\n                    error_2 = _a.sent();\r\n                    dispatch({ type: 'ERROR', error: loginError(error_2) });\r\n                    return [2 /*return*/];\r\n                case 4: return [4 /*yield*/, client.getUser()];\r\n                case 5:\r\n                    user = _a.sent();\r\n                    dispatch({ type: 'LOGIN_POPUP_COMPLETE', user: user });\r\n                    return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [client]);\r\n    var logout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (opts) {\r\n        if (opts === void 0) { opts = {}; }\r\n        return __awaiter(void 0, void 0, void 0, function () {\r\n            return __generator(this, function (_a) {\r\n                switch (_a.label) {\r\n                    case 0: return [4 /*yield*/, client.logout(opts)];\r\n                    case 1:\r\n                        _a.sent();\r\n                        if (opts.openUrl || opts.openUrl === false) {\r\n                            dispatch({ type: 'LOGOUT' });\r\n                        }\r\n                        return [2 /*return*/];\r\n                }\r\n            });\r\n        });\r\n    }, [client]);\r\n    var getAccessTokenSilently = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    function (opts) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var token, error_3, _a;\r\n        var _b;\r\n        return __generator(this, function (_c) {\r\n            switch (_c.label) {\r\n                case 0:\r\n                    _c.trys.push([0, 2, 3, 5]);\r\n                    return [4 /*yield*/, client.getTokenSilently(opts)];\r\n                case 1:\r\n                    token = _c.sent();\r\n                    return [3 /*break*/, 5];\r\n                case 2:\r\n                    error_3 = _c.sent();\r\n                    throw tokenError(error_3);\r\n                case 3:\r\n                    _a = dispatch;\r\n                    _b = {\r\n                        type: 'GET_ACCESS_TOKEN_COMPLETE'\r\n                    };\r\n                    return [4 /*yield*/, client.getUser()];\r\n                case 4:\r\n                    _a.apply(void 0, [(_b.user = _c.sent(),\r\n                            _b)]);\r\n                    return [7 /*endfinally*/];\r\n                case 5: return [2 /*return*/, token];\r\n            }\r\n        });\r\n    }); }, [client]);\r\n    var getAccessTokenWithPopup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (opts, config) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var token, error_4, _a;\r\n        var _b;\r\n        return __generator(this, function (_c) {\r\n            switch (_c.label) {\r\n                case 0:\r\n                    _c.trys.push([0, 2, 3, 5]);\r\n                    return [4 /*yield*/, client.getTokenWithPopup(opts, config)];\r\n                case 1:\r\n                    token = _c.sent();\r\n                    return [3 /*break*/, 5];\r\n                case 2:\r\n                    error_4 = _c.sent();\r\n                    throw tokenError(error_4);\r\n                case 3:\r\n                    _a = dispatch;\r\n                    _b = {\r\n                        type: 'GET_ACCESS_TOKEN_COMPLETE'\r\n                    };\r\n                    return [4 /*yield*/, client.getUser()];\r\n                case 4:\r\n                    _a.apply(void 0, [(_b.user = _c.sent(),\r\n                            _b)]);\r\n                    return [7 /*endfinally*/];\r\n                case 5: return [2 /*return*/, token];\r\n            }\r\n        });\r\n    }); }, [client]);\r\n    var getIdTokenClaims = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () { return client.getIdTokenClaims(); }, [client]);\r\n    var handleRedirectCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (url) { return __awaiter(void 0, void 0, void 0, function () {\r\n        var error_5, _a;\r\n        var _b;\r\n        return __generator(this, function (_c) {\r\n            switch (_c.label) {\r\n                case 0:\r\n                    _c.trys.push([0, 2, 3, 5]);\r\n                    return [4 /*yield*/, client.handleRedirectCallback(url)];\r\n                case 1: return [2 /*return*/, _c.sent()];\r\n                case 2:\r\n                    error_5 = _c.sent();\r\n                    throw tokenError(error_5);\r\n                case 3:\r\n                    _a = dispatch;\r\n                    _b = {\r\n                        type: 'HANDLE_REDIRECT_COMPLETE'\r\n                    };\r\n                    return [4 /*yield*/, client.getUser()];\r\n                case 4:\r\n                    _a.apply(void 0, [(_b.user = _c.sent(),\r\n                            _b)]);\r\n                    return [7 /*endfinally*/];\r\n                case 5: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); }, [client]);\r\n    var contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\r\n        return __assign(__assign({}, state), { getAccessTokenSilently: getAccessTokenSilently, getAccessTokenWithPopup: getAccessTokenWithPopup, getIdTokenClaims: getIdTokenClaims, loginWithRedirect: loginWithRedirect, loginWithPopup: loginWithPopup, logout: logout, handleRedirectCallback: handleRedirectCallback });\r\n    }, [\r\n        state,\r\n        getAccessTokenSilently,\r\n        getAccessTokenWithPopup,\r\n        getIdTokenClaims,\r\n        loginWithRedirect,\r\n        loginWithPopup,\r\n        logout,\r\n        handleRedirectCallback,\r\n    ]);\r\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(context.Provider, { value: contextValue }, children);\r\n};\n\n/**\r\n * ```js\r\n * const {\r\n *   // Auth state:\r\n *   error,\r\n *   isAuthenticated,\r\n *   isLoading,\r\n *   user,\r\n *   // Auth methods:\r\n *   getAccessTokenSilently,\r\n *   getAccessTokenWithPopup,\r\n *   getIdTokenClaims,\r\n *   loginWithRedirect,\r\n *   loginWithPopup,\r\n *   logout,\r\n * } = useAuth0<TUser>();\r\n * ```\r\n *\r\n * Use the `useAuth0` hook in your components to access the auth state and methods.\r\n *\r\n * TUser is an optional type param to provide a type to the `user` field.\r\n */\r\nvar useAuth0 = function (context) {\r\n    if (context === void 0) { context = Auth0Context; }\r\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(context);\r\n};\n\n/**\r\n * ```jsx\r\n * class MyComponent extends Component {\r\n *   render() {\r\n *     // Access the auth context from the `auth0` prop\r\n *     const { user } = this.props.auth0;\r\n *     return <div>Hello {user.name}!</div>\r\n *   }\r\n * }\r\n * // Wrap your class component in withAuth0\r\n * export default withAuth0(MyComponent);\r\n * ```\r\n *\r\n * Wrap your class components in this Higher Order Component to give them access to the Auth0Context.\r\n *\r\n * Providing a context as the second argument allows you to configure the Auth0Provider the Auth0Context\r\n * should come from f you have multiple within your application.\r\n */\r\nvar withAuth0 = function (Component, context) {\r\n    if (context === void 0) { context = Auth0Context; }\r\n    return function WithAuth(props) {\r\n        return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(context.Consumer, null, function (auth) { return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Component, __assign({}, props, { auth0: auth }))); }));\r\n    };\r\n};\n\n/**\r\n * @ignore\r\n */\r\nvar defaultOnRedirecting = function () { return react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null); };\r\n/**\r\n* @ignore\r\n*/\r\nvar defaultOnBeforeAuthentication = function () { return __awaiter(void 0, void 0, void 0, function () { return __generator(this, function (_a) {\r\n    return [2 /*return*/];\r\n}); }); };\r\n/**\r\n * @ignore\r\n */\r\nvar defaultReturnTo = function () {\r\n    return \"\".concat(window.location.pathname).concat(window.location.search);\r\n};\r\n/**\r\n * ```js\r\n * const MyProtectedComponent = withAuthenticationRequired(MyComponent);\r\n * ```\r\n *\r\n * When you wrap your components in this Higher Order Component and an anonymous user visits your component\r\n * they will be redirected to the login page; after login they will be returned to the page they were redirected from.\r\n */\r\nvar withAuthenticationRequired = function (Component, options) {\r\n    if (options === void 0) { options = {}; }\r\n    return function WithAuthenticationRequired(props) {\r\n        var _this = this;\r\n        var _a = options.returnTo, returnTo = _a === void 0 ? defaultReturnTo : _a, _b = options.onRedirecting, onRedirecting = _b === void 0 ? defaultOnRedirecting : _b, _c = options.onBeforeAuthentication, onBeforeAuthentication = _c === void 0 ? defaultOnBeforeAuthentication : _c, loginOptions = options.loginOptions, _d = options.context, context = _d === void 0 ? Auth0Context : _d;\r\n        var _e = useAuth0(context), isAuthenticated = _e.isAuthenticated, isLoading = _e.isLoading, loginWithRedirect = _e.loginWithRedirect;\r\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\r\n            if (isLoading || isAuthenticated) {\r\n                return;\r\n            }\r\n            var opts = __assign(__assign({}, loginOptions), { appState: __assign(__assign({}, (loginOptions && loginOptions.appState)), { returnTo: typeof returnTo === 'function' ? returnTo() : returnTo }) });\r\n            (function () { return __awaiter(_this, void 0, void 0, function () {\r\n                return __generator(this, function (_a) {\r\n                    switch (_a.label) {\r\n                        case 0: return [4 /*yield*/, onBeforeAuthentication()];\r\n                        case 1:\r\n                            _a.sent();\r\n                            return [4 /*yield*/, loginWithRedirect(opts)];\r\n                        case 2:\r\n                            _a.sent();\r\n                            return [2 /*return*/];\r\n                    }\r\n                });\r\n            }); })();\r\n        }, [\r\n            isLoading,\r\n            isAuthenticated,\r\n            loginWithRedirect,\r\n            onBeforeAuthentication,\r\n            loginOptions,\r\n            returnTo,\r\n        ]);\r\n        return isAuthenticated ? react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Component, __assign({}, props)) : onRedirecting();\r\n    };\r\n};\n\n\n//# sourceMappingURL=auth0-react.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@auth0/auth0-react/dist/auth0-react.esm.js\n");

/***/ })

};
;