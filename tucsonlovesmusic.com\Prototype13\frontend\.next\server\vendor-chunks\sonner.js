"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner";
exports.ids = ["vendor-chunks/sonner"];
exports.modules = {

/***/ "(ssr)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Te),\n/* harmony export */   toast: () => (/* binding */ Jt),\n/* harmony export */   useSonner: () => (/* binding */ we)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ \n\n\nvar Ct = (s)=>{\n    switch(s){\n        case \"success\":\n            return $t;\n        case \"info\":\n            return _t;\n        case \"warning\":\n            return Wt;\n        case \"error\":\n            return Ut;\n        default:\n            return null;\n    }\n}, Ft = Array(12).fill(0), It = ({ visible: s })=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-loading-wrapper\",\n        \"data-visible\": s\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, Ft.map((o, t)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${t}`\n        })))), $t = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n})), Wt = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n})), _t = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n})), Ut = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\n\nvar Dt = ()=>{\n    let [s, o] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Dt.useEffect\": ()=>{\n            let t = {\n                \"Dt.useEffect.t\": ()=>{\n                    o(document.hidden);\n                }\n            }[\"Dt.useEffect.t\"];\n            return document.addEventListener(\"visibilitychange\", t), ({\n                \"Dt.useEffect\": ()=>window.removeEventListener(\"visibilitychange\", t)\n            })[\"Dt.useEffect\"];\n        }\n    }[\"Dt.useEffect\"], []), s;\n};\nvar ct = 1, ut = class {\n    constructor(){\n        this.subscribe = (o)=>(this.subscribers.push(o), ()=>{\n                let t = this.subscribers.indexOf(o);\n                this.subscribers.splice(t, 1);\n            });\n        this.publish = (o)=>{\n            this.subscribers.forEach((t)=>t(o));\n        };\n        this.addToast = (o)=>{\n            this.publish(o), this.toasts = [\n                ...this.toasts,\n                o\n            ];\n        };\n        this.create = (o)=>{\n            var b;\n            let { message: t, ...n } = o, h = typeof (o == null ? void 0 : o.id) == \"number\" || ((b = o.id) == null ? void 0 : b.length) > 0 ? o.id : ct++, u = this.toasts.find((d)=>d.id === h), g = o.dismissible === void 0 ? !0 : o.dismissible;\n            return u ? this.toasts = this.toasts.map((d)=>d.id === h ? (this.publish({\n                    ...d,\n                    ...o,\n                    id: h,\n                    title: t\n                }), {\n                    ...d,\n                    ...o,\n                    id: h,\n                    dismissible: g,\n                    title: t\n                }) : d) : this.addToast({\n                title: t,\n                ...n,\n                dismissible: g,\n                id: h\n            }), h;\n        };\n        this.dismiss = (o)=>(o || this.toasts.forEach((t)=>{\n                this.subscribers.forEach((n)=>n({\n                        id: t.id,\n                        dismiss: !0\n                    }));\n            }), this.subscribers.forEach((t)=>t({\n                    id: o,\n                    dismiss: !0\n                })), o);\n        this.message = (o, t)=>this.create({\n                ...t,\n                message: o\n            });\n        this.error = (o, t)=>this.create({\n                ...t,\n                message: o,\n                type: \"error\"\n            });\n        this.success = (o, t)=>this.create({\n                ...t,\n                type: \"success\",\n                message: o\n            });\n        this.info = (o, t)=>this.create({\n                ...t,\n                type: \"info\",\n                message: o\n            });\n        this.warning = (o, t)=>this.create({\n                ...t,\n                type: \"warning\",\n                message: o\n            });\n        this.loading = (o, t)=>this.create({\n                ...t,\n                type: \"loading\",\n                message: o\n            });\n        this.promise = (o, t)=>{\n            if (!t) return;\n            let n;\n            t.loading !== void 0 && (n = this.create({\n                ...t,\n                promise: o,\n                type: \"loading\",\n                message: t.loading,\n                description: typeof t.description != \"function\" ? t.description : void 0\n            }));\n            let h = o instanceof Promise ? o : o(), u = n !== void 0;\n            return h.then(async (g)=>{\n                if (Ot(g) && !g.ok) {\n                    u = !1;\n                    let b = typeof t.error == \"function\" ? await t.error(`HTTP error! status: ${g.status}`) : t.error, d = typeof t.description == \"function\" ? await t.description(`HTTP error! status: ${g.status}`) : t.description;\n                    this.create({\n                        id: n,\n                        type: \"error\",\n                        message: b,\n                        description: d\n                    });\n                } else if (t.success !== void 0) {\n                    u = !1;\n                    let b = typeof t.success == \"function\" ? await t.success(g) : t.success, d = typeof t.description == \"function\" ? await t.description(g) : t.description;\n                    this.create({\n                        id: n,\n                        type: \"success\",\n                        message: b,\n                        description: d\n                    });\n                }\n            }).catch(async (g)=>{\n                if (t.error !== void 0) {\n                    u = !1;\n                    let b = typeof t.error == \"function\" ? await t.error(g) : t.error, d = typeof t.description == \"function\" ? await t.description(g) : t.description;\n                    this.create({\n                        id: n,\n                        type: \"error\",\n                        message: b,\n                        description: d\n                    });\n                }\n            }).finally(()=>{\n                var g;\n                u && (this.dismiss(n), n = void 0), (g = t.finally) == null || g.call(t);\n            }), n;\n        };\n        this.custom = (o, t)=>{\n            let n = (t == null ? void 0 : t.id) || ct++;\n            return this.create({\n                jsx: o(n),\n                id: n,\n                ...t\n            }), n;\n        };\n        this.subscribers = [], this.toasts = [];\n    }\n}, v = new ut, Vt = (s, o)=>{\n    let t = (o == null ? void 0 : o.id) || ct++;\n    return v.addToast({\n        title: s,\n        ...o,\n        id: t\n    }), t;\n}, Ot = (s)=>s && typeof s == \"object\" && \"ok\" in s && typeof s.ok == \"boolean\" && \"status\" in s && typeof s.status == \"number\", Kt = Vt, Xt = ()=>v.toasts, Jt = Object.assign(Kt, {\n    success: v.success,\n    info: v.info,\n    warning: v.warning,\n    error: v.error,\n    custom: v.custom,\n    message: v.message,\n    promise: v.promise,\n    dismiss: v.dismiss,\n    loading: v.loading\n}, {\n    getHistory: Xt\n});\nfunction ft(s, { insertAt: o } = {}) {\n    if (!s || typeof document == \"undefined\") return;\n    let t = document.head || document.getElementsByTagName(\"head\")[0], n = document.createElement(\"style\");\n    n.type = \"text/css\", o === \"top\" && t.firstChild ? t.insertBefore(n, t.firstChild) : t.appendChild(n), n.styleSheet ? n.styleSheet.cssText = s : n.appendChild(document.createTextNode(s));\n}\nft(`:where(html[dir=\"ltr\"]),:where([data-sonner-toaster][dir=\"ltr\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\"rtl\"]),:where([data-sonner-toaster][dir=\"rtl\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position=\"right\"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position=\"left\"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position=\"center\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\"top\"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position=\"bottom\"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\"true\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\"top\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\"bottom\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\"true\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\"dark\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\"true\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\"true\"]):before{content:\"\";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\"top\"][data-swiping=\"true\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\"bottom\"][data-swiping=\"true\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\"false\"][data-removed=\"true\"]):before{content:\"\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\"\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\"true\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\"false\"][data-front=\"false\"][data-styled=\"true\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\"false\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\"true\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"true\"][data-swipe-out=\"false\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"true\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"][data-swipe-out=\"false\"][data-expanded=\"false\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\"true\"][data-front=\"false\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\n`);\nfunction U(s) {\n    return s.label !== void 0;\n}\nvar qt = 3, Qt = \"32px\", Zt = 4e3, te = 356, ee = 14, oe = 20, ae = 200;\nfunction ne(...s) {\n    return s.filter(Boolean).join(\" \");\n}\nvar se = (s)=>{\n    var yt, xt, vt, wt, Tt, St, Rt, Et, Nt, Pt;\n    let { invert: o, toast: t, unstyled: n, interacting: h, setHeights: u, visibleToasts: g, heights: b, index: d, toasts: q, expanded: $, removeToast: V, defaultRichColors: Q, closeButton: i, style: O, cancelButtonStyle: K, actionButtonStyle: Z, className: tt = \"\", descriptionClassName: et = \"\", duration: X, position: ot, gap: w, loadingIcon: j, expandByDefault: W, classNames: r, icons: I, closeButtonAriaLabel: at = \"Close toast\", pauseWhenPageIsHidden: k, cn: T } = s, [z, nt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [D, H] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [st, N] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [M, rt] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [c, m] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), [y, S] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0), A = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), l = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), _ = d === 0, J = d + 1 <= g, x = t.type, P = t.dismissible !== !1, Mt = t.className || \"\", At = t.descriptionClassName || \"\", G = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"se.useMemo[G]\": ()=>b.findIndex({\n                \"se.useMemo[G]\": (a)=>a.toastId === t.id\n            }[\"se.useMemo[G]\"]) || 0\n    }[\"se.useMemo[G]\"], [\n        b,\n        t.id\n    ]), Lt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"se.useMemo[Lt]\": ()=>{\n            var a;\n            return (a = t.closeButton) != null ? a : i;\n        }\n    }[\"se.useMemo[Lt]\"], [\n        t.closeButton,\n        i\n    ]), mt = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"se.useMemo[mt]\": ()=>t.duration || X || Zt\n    }[\"se.useMemo[mt]\"], [\n        t.duration,\n        X\n    ]), it = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), Y = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), pt = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0), F = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), [gt, zt] = ot.split(\"-\"), ht = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"se.useMemo[ht]\": ()=>b.reduce({\n                \"se.useMemo[ht]\": (a, f, p)=>p >= G ? a : a + f.height\n            }[\"se.useMemo[ht]\"], 0)\n    }[\"se.useMemo[ht]\"], [\n        b,\n        G\n    ]), bt = Dt(), jt = t.invert || o, lt = x === \"loading\";\n    Y.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"se.useMemo\": ()=>G * w + ht\n    }[\"se.useMemo\"], [\n        G,\n        ht\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"se.useEffect\": ()=>{\n            nt(!0);\n        }\n    }[\"se.useEffect\"], []), react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"se.useLayoutEffect\": ()=>{\n            if (!z) return;\n            let a = l.current, f = a.style.height;\n            a.style.height = \"auto\";\n            let p = a.getBoundingClientRect().height;\n            a.style.height = f, S(p), u({\n                \"se.useLayoutEffect\": (B)=>B.find({\n                        \"se.useLayoutEffect\": (R)=>R.toastId === t.id\n                    }[\"se.useLayoutEffect\"]) ? B.map({\n                        \"se.useLayoutEffect\": (R)=>R.toastId === t.id ? {\n                                ...R,\n                                height: p\n                            } : R\n                    }[\"se.useLayoutEffect\"]) : [\n                        {\n                            toastId: t.id,\n                            height: p,\n                            position: t.position\n                        },\n                        ...B\n                    ]\n            }[\"se.useLayoutEffect\"]);\n        }\n    }[\"se.useLayoutEffect\"], [\n        z,\n        t.title,\n        t.description,\n        u,\n        t.id\n    ]);\n    let L = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"se.useCallback[L]\": ()=>{\n            H(!0), m(Y.current), u({\n                \"se.useCallback[L]\": (a)=>a.filter({\n                        \"se.useCallback[L]\": (f)=>f.toastId !== t.id\n                    }[\"se.useCallback[L]\"])\n            }[\"se.useCallback[L]\"]), setTimeout({\n                \"se.useCallback[L]\": ()=>{\n                    V(t);\n                }\n            }[\"se.useCallback[L]\"], ae);\n        }\n    }[\"se.useCallback[L]\"], [\n        t,\n        V,\n        u,\n        Y\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"se.useEffect\": ()=>{\n            if (t.promise && x === \"loading\" || t.duration === 1 / 0 || t.type === \"loading\") return;\n            let a, f = mt;\n            return $ || h || k && bt ? ({\n                \"se.useEffect\": ()=>{\n                    if (pt.current < it.current) {\n                        let C = new Date().getTime() - it.current;\n                        f = f - C;\n                    }\n                    pt.current = new Date().getTime();\n                }\n            })[\"se.useEffect\"]() : ({\n                \"se.useEffect\": ()=>{\n                    f !== 1 / 0 && (it.current = new Date().getTime(), a = setTimeout({\n                        \"se.useEffect\": ()=>{\n                            var C;\n                            (C = t.onAutoClose) == null || C.call(t, t), L();\n                        }\n                    }[\"se.useEffect\"], f));\n                }\n            })[\"se.useEffect\"](), ({\n                \"se.useEffect\": ()=>clearTimeout(a)\n            })[\"se.useEffect\"];\n        }\n    }[\"se.useEffect\"], [\n        $,\n        h,\n        W,\n        t,\n        mt,\n        L,\n        t.promise,\n        x,\n        k,\n        bt\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"se.useEffect\": ()=>{\n            let a = l.current;\n            if (a) {\n                let f = a.getBoundingClientRect().height;\n                return S(f), u({\n                    \"se.useEffect\": (p)=>[\n                            {\n                                toastId: t.id,\n                                height: f,\n                                position: t.position\n                            },\n                            ...p\n                        ]\n                }[\"se.useEffect\"]), ({\n                    \"se.useEffect\": ()=>u({\n                            \"se.useEffect\": (p)=>p.filter({\n                                    \"se.useEffect\": (B)=>B.toastId !== t.id\n                                }[\"se.useEffect\"])\n                        }[\"se.useEffect\"])\n                })[\"se.useEffect\"];\n            }\n        }\n    }[\"se.useEffect\"], [\n        u,\n        t.id\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"se.useEffect\": ()=>{\n            t.delete && L();\n        }\n    }[\"se.useEffect\"], [\n        L,\n        t.delete\n    ]);\n    function Yt() {\n        return I != null && I.loading ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loader\",\n            \"data-visible\": x === \"loading\"\n        }, I.loading) : j ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loader\",\n            \"data-visible\": x === \"loading\"\n        }, j) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(It, {\n            visible: x === \"loading\"\n        });\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        \"aria-live\": t.important ? \"assertive\" : \"polite\",\n        \"aria-atomic\": \"true\",\n        role: \"status\",\n        tabIndex: 0,\n        ref: l,\n        className: T(tt, Mt, r == null ? void 0 : r.toast, (yt = t == null ? void 0 : t.classNames) == null ? void 0 : yt.toast, r == null ? void 0 : r.default, r == null ? void 0 : r[x], (xt = t == null ? void 0 : t.classNames) == null ? void 0 : xt[x]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (vt = t.richColors) != null ? vt : Q,\n        \"data-styled\": !(t.jsx || t.unstyled || n),\n        \"data-mounted\": z,\n        \"data-promise\": !!t.promise,\n        \"data-removed\": D,\n        \"data-visible\": J,\n        \"data-y-position\": gt,\n        \"data-x-position\": zt,\n        \"data-index\": d,\n        \"data-front\": _,\n        \"data-swiping\": st,\n        \"data-dismissible\": P,\n        \"data-type\": x,\n        \"data-invert\": jt,\n        \"data-swipe-out\": M,\n        \"data-expanded\": !!($ || W && z),\n        style: {\n            \"--index\": d,\n            \"--toasts-before\": d,\n            \"--z-index\": q.length - d,\n            \"--offset\": `${D ? c : Y.current}px`,\n            \"--initial-height\": W ? \"auto\" : `${y}px`,\n            ...O,\n            ...t.style\n        },\n        onPointerDown: (a)=>{\n            lt || !P || (A.current = new Date, m(Y.current), a.target.setPointerCapture(a.pointerId), a.target.tagName !== \"BUTTON\" && (N(!0), F.current = {\n                x: a.clientX,\n                y: a.clientY\n            }));\n        },\n        onPointerUp: ()=>{\n            var B, C, R, dt;\n            if (M || !P) return;\n            F.current = null;\n            let a = Number(((B = l.current) == null ? void 0 : B.style.getPropertyValue(\"--swipe-amount\").replace(\"px\", \"\")) || 0), f = new Date().getTime() - ((C = A.current) == null ? void 0 : C.getTime()), p = Math.abs(a) / f;\n            if (Math.abs(a) >= oe || p > .11) {\n                m(Y.current), (R = t.onDismiss) == null || R.call(t, t), L(), rt(!0);\n                return;\n            }\n            (dt = l.current) == null || dt.style.setProperty(\"--swipe-amount\", \"0px\"), N(!1);\n        },\n        onPointerMove: (a)=>{\n            var Bt;\n            if (!F.current || !P) return;\n            let f = a.clientY - F.current.y, p = a.clientX - F.current.x, C = (gt === \"top\" ? Math.min : Math.max)(0, f), R = a.pointerType === \"touch\" ? 10 : 2;\n            Math.abs(C) > R ? (Bt = l.current) == null || Bt.style.setProperty(\"--swipe-amount\", `${f}px`) : Math.abs(p) > R && (F.current = null);\n        }\n    }, Lt && !t.jsx ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": at,\n        \"data-disabled\": lt,\n        \"data-close-button\": !0,\n        onClick: lt || !P ? ()=>{} : ()=>{\n            var a;\n            L(), (a = t.onDismiss) == null || a.call(t, t);\n        },\n        className: T(r == null ? void 0 : r.closeButton, (wt = t == null ? void 0 : t.classNames) == null ? void 0 : wt.closeButton)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"12\",\n        height: \"12\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"1.5\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\"\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n        x1: \"18\",\n        y1: \"6\",\n        x2: \"6\",\n        y2: \"18\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n        x1: \"6\",\n        y1: \"6\",\n        x2: \"18\",\n        y2: \"18\"\n    }))) : null, t.jsx || /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.title) ? t.jsx || t.title : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, x || t.icon || t.promise ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: T(r == null ? void 0 : r.icon, (Tt = t == null ? void 0 : t.classNames) == null ? void 0 : Tt.icon)\n    }, t.promise || t.type === \"loading\" && !t.icon ? t.icon || Yt() : null, t.type !== \"loading\" ? t.icon || (I == null ? void 0 : I[x]) || Ct(x) : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: T(r == null ? void 0 : r.content, (St = t == null ? void 0 : t.classNames) == null ? void 0 : St.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: T(r == null ? void 0 : r.title, (Rt = t == null ? void 0 : t.classNames) == null ? void 0 : Rt.title)\n    }, t.title), t.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: T(et, At, r == null ? void 0 : r.description, (Et = t == null ? void 0 : t.classNames) == null ? void 0 : Et.description)\n    }, t.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.cancel) ? t.cancel : t.cancel && U(t.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-cancel\": !0,\n        style: t.cancelButtonStyle || K,\n        onClick: (a)=>{\n            var f, p;\n            U(t.cancel) && P && ((p = (f = t.cancel).onClick) == null || p.call(f, a), L());\n        },\n        className: T(r == null ? void 0 : r.cancelButton, (Nt = t == null ? void 0 : t.classNames) == null ? void 0 : Nt.cancelButton)\n    }, t.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(t.action) ? t.action : t.action && U(t.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": !0,\n        \"data-action\": !0,\n        style: t.actionButtonStyle || Z,\n        onClick: (a)=>{\n            var f, p;\n            U(t.action) && (a.defaultPrevented || ((p = (f = t.action).onClick) == null || p.call(f, a), L()));\n        },\n        className: T(r == null ? void 0 : r.actionButton, (Pt = t == null ? void 0 : t.classNames) == null ? void 0 : Pt.actionButton)\n    }, t.action.label) : null));\n};\nfunction Ht() {\n    if (true) return \"ltr\";\n    let s = document.documentElement.getAttribute(\"dir\");\n    return s === \"auto\" || !s ? window.getComputedStyle(document.documentElement).direction : s;\n}\nfunction we() {\n    let [s, o] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"we.useEffect\": ()=>v.subscribe({\n                \"we.useEffect\": (t)=>{\n                    o({\n                        \"we.useEffect\": (n)=>{\n                            if (\"dismiss\" in t && t.dismiss) return n.filter({\n                                \"we.useEffect\": (u)=>u.id !== t.id\n                            }[\"we.useEffect\"]);\n                            let h = n.findIndex({\n                                \"we.useEffect.h\": (u)=>u.id === t.id\n                            }[\"we.useEffect.h\"]);\n                            if (h !== -1) {\n                                let u = [\n                                    ...n\n                                ];\n                                return u[h] = {\n                                    ...u[h],\n                                    ...t\n                                }, u;\n                            } else return [\n                                t,\n                                ...n\n                            ];\n                        }\n                    }[\"we.useEffect\"]);\n                }\n            }[\"we.useEffect\"])\n    }[\"we.useEffect\"], []), {\n        toasts: s\n    };\n}\nvar Te = (s)=>{\n    let { invert: o, position: t = \"bottom-right\", hotkey: n = [\n        \"altKey\",\n        \"KeyT\"\n    ], expand: h, closeButton: u, className: g, offset: b, theme: d = \"light\", richColors: q, duration: $, style: V, visibleToasts: Q = qt, toastOptions: i, dir: O = Ht(), gap: K = ee, loadingIcon: Z, icons: tt, containerAriaLabel: et = \"Notifications\", pauseWhenPageIsHidden: X, cn: ot = ne } = s, [w, j] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), W = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Te.useMemo[W]\": ()=>Array.from(new Set([\n                t\n            ].concat(w.filter({\n                \"Te.useMemo[W]\": (c)=>c.position\n            }[\"Te.useMemo[W]\"]).map({\n                \"Te.useMemo[W]\": (c)=>c.position\n            }[\"Te.useMemo[W]\"]))))\n    }[\"Te.useMemo[W]\"], [\n        w,\n        t\n    ]), [r, I] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]), [at, k] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [T, z] = react__WEBPACK_IMPORTED_MODULE_0__.useState(!1), [nt, D] = react__WEBPACK_IMPORTED_MODULE_0__.useState(d !== \"system\" ? d :  false ? 0 : \"light\"), H = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), st = n.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\"), N = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null), M = react__WEBPACK_IMPORTED_MODULE_0__.useRef(!1), rt = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Te.useCallback[rt]\": (c)=>{\n            var m;\n            (m = w.find({\n                \"Te.useCallback[rt]\": (y)=>y.id === c.id\n            }[\"Te.useCallback[rt]\"])) != null && m.delete || v.dismiss(c.id), j({\n                \"Te.useCallback[rt]\": (y)=>y.filter({\n                        \"Te.useCallback[rt]\": ({ id: S })=>S !== c.id\n                    }[\"Te.useCallback[rt]\"])\n            }[\"Te.useCallback[rt]\"]);\n        }\n    }[\"Te.useCallback[rt]\"], [\n        w\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Te.useEffect\": ()=>v.subscribe({\n                \"Te.useEffect\": (c)=>{\n                    if (c.dismiss) {\n                        j({\n                            \"Te.useEffect\": (m)=>m.map({\n                                    \"Te.useEffect\": (y)=>y.id === c.id ? {\n                                            ...y,\n                                            delete: !0\n                                        } : y\n                                }[\"Te.useEffect\"])\n                        }[\"Te.useEffect\"]);\n                        return;\n                    }\n                    setTimeout({\n                        \"Te.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"Te.useEffect\": ()=>{\n                                    j({\n                                        \"Te.useEffect\": (m)=>{\n                                            let y = m.findIndex({\n                                                \"Te.useEffect.y\": (S)=>S.id === c.id\n                                            }[\"Te.useEffect.y\"]);\n                                            return y !== -1 ? [\n                                                ...m.slice(0, y),\n                                                {\n                                                    ...m[y],\n                                                    ...c\n                                                },\n                                                ...m.slice(y + 1)\n                                            ] : [\n                                                c,\n                                                ...m\n                                            ];\n                                        }\n                                    }[\"Te.useEffect\"]);\n                                }\n                            }[\"Te.useEffect\"]);\n                        }\n                    }[\"Te.useEffect\"]);\n                }\n            }[\"Te.useEffect\"])\n    }[\"Te.useEffect\"], []), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Te.useEffect\": ()=>{\n            if (d !== \"system\") {\n                D(d);\n                return;\n            }\n            d === \"system\" && (window.matchMedia && window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? D(\"dark\") : D(\"light\")),  false && 0;\n        }\n    }[\"Te.useEffect\"], [\n        d\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Te.useEffect\": ()=>{\n            w.length <= 1 && k(!1);\n        }\n    }[\"Te.useEffect\"], [\n        w\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Te.useEffect\": ()=>{\n            let c = {\n                \"Te.useEffect.c\": (m)=>{\n                    var S, A;\n                    n.every({\n                        \"Te.useEffect.c\": (l)=>m[l] || m.code === l\n                    }[\"Te.useEffect.c\"]) && (k(!0), (S = H.current) == null || S.focus()), m.code === \"Escape\" && (document.activeElement === H.current || (A = H.current) != null && A.contains(document.activeElement)) && k(!1);\n                }\n            }[\"Te.useEffect.c\"];\n            return document.addEventListener(\"keydown\", c), ({\n                \"Te.useEffect\": ()=>document.removeEventListener(\"keydown\", c)\n            })[\"Te.useEffect\"];\n        }\n    }[\"Te.useEffect\"], [\n        n\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Te.useEffect\": ()=>{\n            if (H.current) return ({\n                \"Te.useEffect\": ()=>{\n                    N.current && (N.current.focus({\n                        preventScroll: !0\n                    }), N.current = null, M.current = !1);\n                }\n            })[\"Te.useEffect\"];\n        }\n    }[\"Te.useEffect\"], [\n        H.current\n    ]), w.length ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        \"aria-label\": `${et} ${st}`,\n        tabIndex: -1\n    }, W.map((c, m)=>{\n        var A;\n        let [y, S] = c.split(\"-\");\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: c,\n            dir: O === \"auto\" ? Ht() : O,\n            tabIndex: -1,\n            ref: H,\n            className: g,\n            \"data-sonner-toaster\": !0,\n            \"data-theme\": nt,\n            \"data-y-position\": y,\n            \"data-x-position\": S,\n            style: {\n                \"--front-toast-height\": `${((A = r[0]) == null ? void 0 : A.height) || 0}px`,\n                \"--offset\": typeof b == \"number\" ? `${b}px` : b || Qt,\n                \"--width\": `${te}px`,\n                \"--gap\": `${K}px`,\n                ...V\n            },\n            onBlur: (l)=>{\n                M.current && !l.currentTarget.contains(l.relatedTarget) && (M.current = !1, N.current && (N.current.focus({\n                    preventScroll: !0\n                }), N.current = null));\n            },\n            onFocus: (l)=>{\n                l.target instanceof HTMLElement && l.target.dataset.dismissible === \"false\" || M.current || (M.current = !0, N.current = l.relatedTarget);\n            },\n            onMouseEnter: ()=>k(!0),\n            onMouseMove: ()=>k(!0),\n            onMouseLeave: ()=>{\n                T || k(!1);\n            },\n            onPointerDown: (l)=>{\n                l.target instanceof HTMLElement && l.target.dataset.dismissible === \"false\" || z(!0);\n            },\n            onPointerUp: ()=>z(!1)\n        }, w.filter((l)=>!l.position && m === 0 || l.position === c).map((l, _)=>{\n            var J, x;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(se, {\n                key: l.id,\n                icons: tt,\n                index: _,\n                toast: l,\n                defaultRichColors: q,\n                duration: (J = i == null ? void 0 : i.duration) != null ? J : $,\n                className: i == null ? void 0 : i.className,\n                descriptionClassName: i == null ? void 0 : i.descriptionClassName,\n                invert: o,\n                visibleToasts: Q,\n                closeButton: (x = i == null ? void 0 : i.closeButton) != null ? x : u,\n                interacting: T,\n                position: c,\n                style: i == null ? void 0 : i.style,\n                unstyled: i == null ? void 0 : i.unstyled,\n                classNames: i == null ? void 0 : i.classNames,\n                cancelButtonStyle: i == null ? void 0 : i.cancelButtonStyle,\n                actionButtonStyle: i == null ? void 0 : i.actionButtonStyle,\n                removeToast: rt,\n                toasts: w.filter((P)=>P.position == l.position),\n                heights: r.filter((P)=>P.position == l.position),\n                setHeights: I,\n                expandByDefault: h,\n                gap: K,\n                loadingIcon: Z,\n                expanded: at,\n                pauseWhenPageIsHidden: X,\n                cn: ot\n            });\n        }));\n    })) : null;\n};\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sonner/dist/index.mjs\n");

/***/ })

};
;