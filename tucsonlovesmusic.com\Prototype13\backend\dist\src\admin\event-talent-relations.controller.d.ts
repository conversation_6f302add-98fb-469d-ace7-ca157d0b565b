import { DataSource } from 'typeorm';
interface EventMissingTalent {
    id: string;
    name: string;
    date: string;
    venue_name: string;
    azure_talent_ids: string[];
    postgres_talent_count: number;
}
interface EventTalentStatusCounts {
    total: number;
    missingTalents: number;
    potentialFixes: number;
    mismatchedTalents: number;
}
declare class UpdateEventTalentsDto {
    eventId: string;
    talentIds: string[];
}
export declare class EventTalentRelationsController {
    private dataSource;
    private extractSocialLinksFromDescription;
    private azureConfig;
    private azurePoolPromise;
    private connectionRetryAttempts;
    private connectionRetryDelay;
    private lastConnectionFailure;
    private circuitBreakerWindow;
    constructor(dataSource: DataSource);
    private getAzurePool;
    private closeExistingPool;
    private createNewPool;
    getEventTalentStatusCounts(year?: string): Promise<EventTalentStatusCounts>;
    getEventTalentStatusCountsStatus(): Promise<EventTalentStatusCounts>;
    private getEventTalentStatusCountsInternal;
    getEventsWithMissingTalents(year?: string): Promise<EventMissingTalent[]>;
    getEventsWithMismatchedTalents(year?: string): Promise<EventMissingTalent[]>;
    fixEventTalentRelationship(updateDto: UpdateEventTalentsDto): Promise<{
        success: boolean;
        message: string;
    }>;
    fixSingleEventTalentRelationship(eventId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    fixAllEventTalentRelationshipsAggregated(year?: string): Promise<{
        success: boolean;
        message: string;
        stats: {
            totalProcessed: number;
            successfulFixes: number;
            failedFixes: number;
            relationshipsAdded: number;
            relationshipsRemoved: number;
            talentsNotFound: number;
        };
    }>;
    fixEventTalentRelationshipsFromCsv(): Promise<any>;
    fixAllEventTalentRelationships(): Promise<{
        success: boolean;
        message: string;
    }>;
}
export {};
